# CLAUDE.md

This file provides guidance to <PERSON> when working with the Brainless Stats codebase.

## Quick Reference

### Essential Commands
```bash
# Development
pnpm dev                    # Start frontend + backend
pnpm dev:full              # Start full Docker environment
pnpm test                  # Run all tests
pnpm lint                  # Lint all code
pnpm build                 # Build all services

# Backend-specific (from packages/backend/)
uv run brainless-cli dev   # Start backend with Docker
uv run pytest             # Run backend tests
uv run brainless-cli lint  # Backend linting

# Frontend-specific (from frontend/)
pnpm check                 # TypeScript checking
pnpm test:e2e             # Playwright E2E tests
pnpm schema:generate      # Generate API types from OpenAPI
```

### Project Structure
- **`frontend/`**: SvelteKit app (port 3000)
- **`packages/backend/`**: FastAPI server (port 8000)
- **`packages/demo-processing/`**: FastStream microservice
- **`packages/core/`**: Shared Python utilities (`brainless-core`)

### Key Technologies
- **Python**: FastAPI, SQLModel, FastStream, databases library, uv workspaces
- **Frontend**: SvelteKit, TypeScript, Tai<PERSON>wind, shadcn/ui, Drizzle ORM
- **Infrastructure**: Docker Compose, Redis, SQLite databases

## Code Style & Guidelines

### Python Code Style
- Use **SQLModel** for database models (combines SQLAlchemy + Pydantic)
- Use **databases library** for async SQL operations with SQLModel schemas
- Use **specific types everywhere** - avoid `Any` types
- Use **Google-style docstrings**
- Prefer **uv** for dependency management (`uv add`, `uv sync`)
- Use **anyio** library instead of custom async utilities
- Fix code issues directly rather than using `# noqa` comments

### TypeScript/Frontend Style
- Use **ES modules** (import/export), not CommonJS (require)
- **Destructure imports** when possible: `import { foo } from 'bar'`
- Use **strict TypeScript** configuration
- **Exclude shadcn/ui components** from formatting to preserve structure
- Use **OpenAPI-generated types** for API calls

### Testing Strategy
- **Backend**: Prefer integration/API tests over unit tests unless actual logic/algorithms
- **Frontend**: Vitest for unit tests, Playwright for E2E
- Use **testcontainers** for testing where appropriate
- **Always write tests** when implementing new features

### IMPORTANT Workflow Rules
- **Always run linting from repository root** (configs are at root level)
- **Run basedpyright from workspace root**, not individual packages
- **Use package managers** for dependencies (never edit package.json/pyproject.toml manually)
- **Test your changes** before committing

## Testing Instructions

### Running Tests
```bash
# All tests from root
pnpm test                    # Run all tests across packages
pnpm test:coverage          # Tests with coverage reports

# Backend tests (from packages/backend/)
uv run pytest              # All backend tests
uv run pytest tests/test_api.py  # Specific test file
uv run brainless-cli test   # Via CLI tool

# Frontend tests (from frontend/)
pnpm test                   # Vitest unit tests
pnpm test:e2e              # Playwright E2E tests
pnpm test:e2e:ui           # Playwright with UI

# Demo processing tests (from packages/demo-processing/)
uv run pytest              # Demo processing tests
```

### Test Development Workflow
1. **Write tests first** when possible (TDD approach)
2. **Run tests and confirm they fail** before implementing
3. **Implement code to make tests pass**
4. **Verify all tests pass** before committing
5. **Use testcontainers** for integration tests requiring databases

### Key Test Files
- `packages/backend/tests/` - Backend API and integration tests
- `frontend/tests/` - Frontend unit and E2E tests
- `packages/demo-processing/tests/` - Demo processing tests

## Repository Workflow & Etiquette

### Git Workflow
- **Main branch**: `main` (default branch)
- **Branch naming**: Use descriptive names like `feature/demo-upload` or `fix/auth-redirect`
- **Commits**: Use conventional commit format: `type(scope): description`
  - Types: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`
  - Keep first line under 72 characters
  - Use imperative mood: "add feature" not "added feature"
- **NEVER include** Claude Code attribution in commit messages
- **Always test** your changes before committing

### Development Environment
- **Python version**: >=3.12 (backend uses >=3.13)
- **Node.js**: Use pnpm for package management
- **Docker**: Required for full development environment
- **Pre-commit hooks**: Automatically run linting and formatting

### Release Process
- **Unified versioning**: All packages share the same version
- **Changelog**: Always update `CHANGELOG.md` under `[Unreleased]` before releases
- **Release commands**: `pnpm version:patch|minor|major` handles everything automatically
- **CI/CD**: Releases trigger Docker image builds and GitHub releases

## Key Files & Locations

### Core Configuration
- `pyproject.toml` (root) - Python workspace and linting config
- `pnpm-workspace.yaml` - Frontend workspace config
- `docker-compose.dev.yml` - Development environment
- `.env.example` files in root, backend, and frontend

### Backend (`packages/backend/`)
- `src/backend/main.py` - FastAPI app entry point
- `src/backend/models/` - SQLModel database models (combines SQLAlchemy + Pydantic)
- `src/backend/database.py` - Database connection and async operations
- `src/backend/cli.py` - CLI tool (`brainless-cli`)
- `database.db` - SQLite database file

### Frontend (`frontend/`)
- `src/lib/api/schema.d.ts` - Generated OpenAPI types
- `src/lib/db/schema.ts` - Drizzle database schema
- `src/routes/` - SvelteKit pages and API routes
- `local.db` - SQLite database for sessions

### Demo Processing (`packages/demo-processing/`)
- `src/demo_processing/main.py` - FastStream service entry point
- `src/demo_processing/processors/` - CS2 demo parsing logic

### Shared Core (`packages/core/`)
- `src/brainless_core/logging.py` - Structured logging setup
- `src/brainless_core/settings.py` - Configuration management

## Common Development Patterns

### API Development
1. **Define SQLModel schemas** in `packages/backend/src/backend/models/`
2. **Create FastAPI endpoints** using SQLModel for request/response models
3. **Generate frontend types**: `cd frontend && pnpm schema:generate`
4. **Use generated types** in frontend API calls

### Database Operations
- **Backend**: Use SQLModel for type-safe database models + databases library for async operations
- **Frontend**: Use Drizzle ORM for type-safe operations
- **No shared database** - each service manages its own data
- **SQLModel pattern**: Define base models, table models, and CRUD models separately

### Demo Processing Workflow
1. **Backend receives** demo upload request
2. **Queues job** via FastStream + Redis
3. **Demo processing service** handles parsing with awpy/demoparser2
4. **Results stored** in backend database
5. **Frontend polls** or receives updates via API

### Authentication Flow
- **Steam OpenID** authentication via `/login` → `/steam-callback`
- **New users** redirected to `/settings?setup=tracking` for CS2 config
- **Session management** handled by frontend database

## Development Environment Setup

### Docker Development
```bash
pnpm dev:full              # Full Docker environment (recommended)
# Access points:
# - Frontend: http://localhost:3000
# - Backend API: http://localhost:8000
# - API docs: http://localhost:8000/docs
```

### Local Development
```bash
pnpm dev                   # Frontend + backend without Docker
# Requires: Redis running locally, Python >=3.12, Node.js with pnpm
```

## Important Notes & Warnings

### Performance Considerations
- **Demo processing** can be CPU/memory intensive for large demo files
- **Use Docker** for consistent development environment
- **Redis** is required for demo processing queue

### Common Issues
- **Port conflicts**: Ensure ports 3000, 8000, 6379 are available
- **Database migrations**: Run automatically on startup
- **Type generation**: Run `pnpm schema:generate` after backend API changes
- **Pre-commit hooks**: May fail on first run - install with `pre-commit install`

### Security Notes
- **Steam API keys** required for authentication (see .env.example)
- **Demo files** contain sensitive match data - handle appropriately
- **SQLite databases** are local files - backup important data

## Domain-Specific Information

### CS2 Demo Processing
- **Demo formats**: Supports CS2 .dem files via awpy/demoparser2
- **Processing time**: Large demos (>100MB) can take several minutes
- **Output data**: Player stats, round events, economy data, positioning
- **Queue system**: Uses Redis + FastStream for async processing

### Steam Integration
- **Authentication**: Steam OpenID via `/login` → `/steam-callback`
- **CS2 Tracking Setup**: Users need authentication code + match token
- **API endpoints**: `POST /api/users/{user_id}/tracking` for configuration
- **Key files**:
  - `frontend/src/routes/steam-callback/+page.server.ts`
  - `frontend/src/routes/(app)/settings/+page.svelte`

### Release Management
```bash
# Version commands (unified across all packages)
pnpm version:status        # Check current versions
pnpm version:patch         # Bug fixes (0.1.0 → 0.1.1)
pnpm version:minor         # New features (0.1.0 → 0.2.0)
pnpm version:major         # Breaking changes (0.1.0 → 1.0.0)
```

**IMPORTANT**: Always update `CHANGELOG.md` under `[Unreleased]` before creating releases.
