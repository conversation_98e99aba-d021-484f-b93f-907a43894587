Please fix all linting issues across the codebase automatically.

Follow these steps:

1. Run `pnpm lint` from repository root to identify all linting issues
2. Apply automatic fixes where possible:
   - Run `pnpm lint:fix` if available, or individual fixers:
   - `cd frontend && pnpm lint:fix` for frontend issues
   - `cd packages/backend && uv run ruff check --fix .` for backend Python issues
   - `cd packages/backend && uv run ruff format .` for Python formatting
3. Review remaining issues that require manual fixes
4. Fix any remaining issues manually following project style guidelines
5. Run `pnpm lint` again to verify all issues are resolved
6. If changes were made, commit with: `git commit -m "style: fix linting issues"`
7. Show summary of what was fixed

Remember to run linting from the repository root as configs are centralized there.
