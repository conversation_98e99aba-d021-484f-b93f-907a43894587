Please implement the GitHub issue: $ARGUMENTS following Anthropic's best practices for agentic coding.

**IMPORTANT**: Do NOT start coding until you have completed the exploration and planning phases. This workflow is designed to maximize success rate and minimize iterations.

## Phase 1: Exploration & Context Gathering

### 1.1 Issue Analysis
1. **Fetch issue details**: Use `gh issue view $ARGUMENTS` to get complete issue information
2. **Think hard** about the problem scope and requirements
3. **Identify affected components**:
   - Which packages need changes? (frontend, backend, demo-processing, core)
   - What files/modules are involved?
   - Are there dependencies between changes?
4. **Ask clarifying questions** if any requirements are unclear or ambiguous

### 1.2 Codebase Exploration
1. **Search relevant code** using codebase context to understand:
   - Existing patterns and conventions
   - Similar implementations to reference
   - Potential integration points
   - Testing patterns in the affected areas
2. **Identify key files** that will need modification
3. **Understand data flow** between frontend, backend, and demo-processing if applicable

### 1.3 Technical Context
1. **Review related issues/PRs** using `gh` CLI for additional context
2. **Check recent changes** in affected areas using `git log`
3. **Verify current system state** by running tests and checking for existing issues

## Phase 2: Strategic Planning

### 2.1 Implementation Strategy
1. **Think harder** about the optimal approach considering:
   - Existing codebase patterns and architecture
   - Potential breaking changes and backwards compatibility
   - Performance implications
   - Security considerations
   - Testing strategy

2. **Create a detailed implementation plan** covering:
   - **Database changes**: SQLModel models, migrations, schema updates
   - **Backend API changes**: FastAPI endpoints, request/response models
   - **Frontend changes**: SvelteKit components, routes, API integration
   - **Demo processing changes**: FastStream handlers, processing logic
   - **Testing approach**: Integration tests, E2E tests, manual testing
   - **Documentation updates**: README, CHANGELOG, API docs

3. **Identify potential risks** and mitigation strategies
4. **Estimate complexity** and break down into logical commits

### 2.2 Plan Validation
1. **Create a checklist** in a markdown scratchpad for tracking progress
2. **Verify the plan** addresses all acceptance criteria
3. **Confirm the approach** aligns with project conventions
4. **DO NOT PROCEED** until the plan is solid and complete

## Phase 3: Branch Setup & Environment

### 3.1 Git Workflow Setup
1. **Ensure clean state**: `git status` and commit/stash any changes
2. **Update main branch**: `git checkout main && git pull origin main`
3. **Create feature branch**: Use descriptive name like `feature/issue-$ARGUMENTS-description`
4. **Verify development environment**: Run `pnpm dev` or `pnpm dev:full` to ensure everything works

### 3.2 Pre-Implementation Checks
1. **Run baseline tests**: `pnpm test` to ensure starting from green state
2. **Check linting**: `pnpm lint` from repository root
3. **Verify TypeScript**: `cd frontend && pnpm check`

## Phase 4: Test-Driven Implementation

### 4.1 Test-First Development
1. **Write failing tests first** based on expected behavior:
   - Backend: API integration tests in `packages/backend/tests/`
   - Frontend: Component tests and E2E tests as appropriate
   - Demo processing: Unit/integration tests for new functionality

2. **Run tests and confirm they fail** appropriately
3. **Commit the tests**: Use conventional commit format
   ```bash
   git add . && git commit -m "test(scope): add tests for issue #$ARGUMENTS"
   ```

### 4.2 Iterative Implementation
1. **Implement in logical order**:
   - Core/shared utilities first (`packages/core/`)
   - Database models and schemas (`packages/backend/src/backend/models/`)
   - Backend API endpoints (`packages/backend/src/backend/`)
   - Frontend components and integration (`frontend/src/`)
   - Demo processing changes if needed (`packages/demo-processing/`)

2. **Test frequently** during implementation:
   - Run specific tests: `uv run pytest tests/test_specific.py`
   - Check integration: `pnpm test`
   - Manual testing in development environment

3. **Commit often** with focused, descriptive messages:
   ```bash
   git add . && git commit -m "feat(backend): implement API endpoint for issue #$ARGUMENTS"
   ```

### 4.3 Integration & Verification
1. **Generate frontend types** after backend changes: `cd frontend && pnpm schema:generate`
2. **Test cross-service integration** if multiple packages are involved
3. **Verify all tests pass**: `pnpm test` from root
4. **Manual testing** in development environment to ensure UX works as expected

## Phase 5: Quality Assurance & Polish

### 5.1 Comprehensive Testing
1. **Run full test suite**: `pnpm test` and `pnpm test:coverage`
2. **Linting and formatting**: `pnpm lint` from repository root
3. **Type checking**:
   - Backend: Run from workspace root (not individual packages)
   - Frontend: `cd frontend && pnpm check`
4. **E2E testing** if UI changes: `cd frontend && pnpm test:e2e`

### 5.2 Code Quality Review
1. **Self-review changes** using `git diff main` or similar
2. **Verify code follows project conventions**:
   - SQLModel for database operations
   - Specific types everywhere (no `Any` types)
   - Proper error handling
   - Google-style docstrings for Python
3. **Clean up debug code** and temporary files
4. **Ensure no sensitive data** is committed

### 5.3 Documentation Updates
1. **Update CHANGELOG.md** under `[Unreleased]` section
2. **Update relevant documentation** (README, API docs, etc.)
3. **Add code comments** for complex logic
4. **Verify examples** and documentation are accurate

## Phase 6: Final Integration & PR

### 6.1 Final Rebase & Cleanup
1. **Fetch latest main**: `git fetch origin main`
2. **Interactive rebase** to clean up commit history: `git rebase -i origin/main`
3. **Resolve conflicts** if any arise during rebase
4. **Final test run** after rebase: `pnpm test && pnpm lint`
5. **Force push with lease**: `git push origin feature-branch-name --force-with-lease`

### 6.2 Pull Request Creation
1. **Create comprehensive PR** using:
   ```bash
   gh pr create --title "feat: implement issue #$ARGUMENTS - brief description" --body "Closes #$ARGUMENTS

   ## Summary
   Brief description of changes

   ## Changes Made
   - List key changes
   - Include any breaking changes

   ## Testing
   - Describe testing performed
   - Include screenshots for UI changes

   ## Checklist
   - [ ] Tests pass
   - [ ] Linting passes
   - [ ] Documentation updated
   - [ ] CHANGELOG.md updated"
   ```

2. **Include visual evidence** for UI changes (screenshots, recordings)
3. **Tag relevant reviewers** if known

## Critical Success Factors

### Workflow Discipline
- **NEVER skip the exploration phase** - it prevents most implementation issues
- **Use thinking keywords** ("think", "think hard", "think harder") for complex decisions
- **Course correct early** - interrupt and redirect if going off track
- **Use `/clear`** between major phases to maintain focused context

### Technical Excellence
- **Follow TDD approach** - tests first, then implementation
- **Use package managers** for dependencies (uv, pnpm) - never edit config files manually
- **Run linting from repository root** - configs are centralized
- **Generate types after API changes** - maintain type safety
- **Use SQLModel patterns** - base models, table models, CRUD models

### Git Hygiene
- **Conventional commits**: `type(scope): description`
- **Focused commits** - one logical change per commit
- **Rebase workflow** - maintain linear history
- **NEVER include** Claude Code attribution
- **Test before committing** - every commit should be deployable

### Communication
- **Be specific** in all instructions and error messages
- **Ask for clarification** when requirements are unclear
- **Document decisions** and trade-offs made
- **Include context** in commit messages and PR descriptions

Remember: This workflow prioritizes correctness and maintainability over speed. Taking time upfront prevents costly iterations later.
