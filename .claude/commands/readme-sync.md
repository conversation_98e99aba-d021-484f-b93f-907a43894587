Please sync the README.md with current project features and structure.

Follow these steps:

1. Read the current README.md file
2. Analyze the codebase structure and identify:
   - Current features and capabilities
   - Package structure and dependencies
   - Available scripts and commands
   - Setup and installation requirements
   - API endpoints and functionality
3. Check package.json files for current scripts and dependencies
4. Review CLAUDE.md for any important setup or workflow information
5. Update README.md sections to reflect current state:
   - Project description and features
   - Installation instructions
   - Development setup
   - Available commands
   - Project structure
   - Technology stack
   - Contributing guidelines
6. Ensure all code examples and commands are accurate and tested
7. Fix any broken links or outdated information
8. Commit changes with: `git commit -m "docs: sync README with current project state"`

Focus on accuracy and keeping the documentation concise but comprehensive.
