Please review all git changes and organize them into logical commits with conventional commit messages.

Follow these steps:

1. Run `git status` and `git diff` to see all current changes
2. Review any open issues with `gh issue list` for context
3. Group related changes into logical commits following conventional commit format:
   - `feat:` for new features
   - `fix:` for bug fixes
   - `docs:` for documentation changes
   - `style:` for formatting/style changes
   - `refactor:` for code refactoring
   - `test:` for adding/updating tests
   - `chore:` for maintenance tasks
4. Stage and commit each group separately with descriptive messages
5. Run `pnpm lint` and `pnpm test` after all commits
6. Show a summary of all commits created

Use `git add -p` for selective staging when needed to separate mixed changes.
