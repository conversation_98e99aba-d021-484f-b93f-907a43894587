Please find and organize all TODO comments in the codebase.

Follow these steps:

1. Search for TODO comments across the codebase:
   - Use `grep -r "TODO\|FIXME\|HACK\|XXX" --include="*.py" --include="*.ts" --include="*.js" --include="*.svelte" --include="*.md" .`
   - Also search for variations like "todo", "Todo", "@todo"
2. Categorize TODOs by:
   - Priority (urgent, normal, low)
   - Component (frontend, backend, demo-processing, core)
   - Type (bug fix, feature, refactor, documentation)
3. Create a markdown file `TODO_SUMMARY.md` with organized findings:
   - Group by category and priority
   - Include file location and line number
   - Add context around each TODO
4. Identify TODOs that should become GitHub issues:
   - Complex features or significant changes
   - Bug fixes that need tracking
   - Items that require team discussion
5. For appropriate TODOs, create GitHub issues:
   - Use `gh issue create` with descriptive titles
   - Include context and file references
   - Add appropriate labels (bug, enhancement, documentation)
6. Update TODO comments to reference created issues where applicable
7. Remove completed or obsolete TODOs
8. Commit changes with: `git commit -m "chore: organize and track TODO items"`

Focus on converting actionable TODOs into proper issue tracking while cleaning up stale comments.
