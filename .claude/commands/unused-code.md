Please find and remove unused code and dependencies across the codebase.

Follow these steps:

1. Find unused Python imports and code:
   - Run `cd packages/backend && uv run ruff check --select F401` for unused imports
   - Use `cd packages/backend && uv run vulture .` if available for dead code detection
   - Check for unused functions, classes, and variables
2. Find unused TypeScript/JavaScript code:
   - Run `cd frontend && pnpm check` to identify unused imports
   - Look for unused components, utilities, and functions
   - Check for unreferenced files in src/ directories
3. Analyze dependencies:
   - Check Python dependencies: `cd packages/backend && uv run pip-audit` or similar
   - Check Node.js dependencies: `cd frontend && pnpm audit`
   - Identify unused packages in package.json and pyproject.toml files
4. Find unused assets and files:
   - Look for unreferenced images, styles, and static files
   - Check for old migration files or test fixtures
   - Identify orphaned configuration files
5. Create a summary report of findings:
   - List unused imports, functions, and files
   - Identify safe-to-remove dependencies
   - Note any files that might be used dynamically
6. Remove safe unused code:
   - Remove unused imports and dead functions
   - Delete unreferenced files and assets
   - Remove unused dependencies (be careful with dev dependencies)
7. Test after cleanup:
   - Run `pnpm test` to ensure nothing breaks
   - Run `pnpm lint` to check for new issues
   - Test key functionality manually
8. Commit changes with: `git commit -m "chore: remove unused code and dependencies"`

Be conservative - when in doubt, keep the code and mark it for manual review.
